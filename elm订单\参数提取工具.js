/**
 * 喜翻参数提取工具
 * 用于从curl命令中提取必要的认证参数
 */

/**
 * 从curl命令中提取参数
 * @param {string} curlCommand - 完整的curl命令
 * @returns {Object} 提取的参数对象
 */
function extractParametersFromCurl(curlCommand) {
    const params = {
        url: '',
        headers: {},
        cookies: {},
        body: {},
        signature: '',
        userId: '',
        passToken: '',
        playletApiSt: '',
        did: '',
        kssig: ''
    };

    try {
        // 提取URL
        const urlMatch = curlCommand.match(/curl\s+(?:-X\s+\w+\s+)?'([^']+)'/);
        if (urlMatch) {
            params.url = urlMatch[1];
        }

        // 提取Headers
        const headerMatches = curlCommand.matchAll(/-H\s+'([^:]+):\s*([^']+)'/g);
        for (const match of headerMatches) {
            const headerName = match[1];
            const headerValue = match[2];
            params.headers[headerName] = headerValue;
            
            // 特殊处理签名
            if (headerName === 'Ks-Sig3') {
                params.signature = headerValue;
            }
        }

        // 提取Cookie
        if (params.headers['Cookie']) {
            const cookieString = params.headers['Cookie'];
            const cookiePairs = cookieString.split(';');
            
            for (const pair of cookiePairs) {
                const [key, value] = pair.split('=').map(s => s.trim());
                if (key && value) {
                    params.cookies[key] = value;
                    
                    // 提取关键cookie值
                    switch (key) {
                        case 'userId':
                            params.userId = value;
                            break;
                        case 'passToken':
                            params.passToken = value;
                            break;
                        case 'playlet.api_st':
                            params.playletApiSt = value;
                            break;
                        case 'did':
                            params.did = value;
                            break;
                        case 'kssig':
                            params.kssig = value;
                            break;
                    }
                }
            }
        }

        // 提取请求体
        const bodyMatch = curlCommand.match(/-d\s+'({[^']+})'/);
        if (bodyMatch) {
            try {
                params.body = JSON.parse(bodyMatch[1]);
            } catch (e) {
                console.warn('请求体JSON解析失败');
            }
        }

    } catch (error) {
        console.error('参数提取失败:', error.message);
    }

    return params;
}

/**
 * 生成配置文件模板
 * @param {Object} params - 提取的参数
 * @param {string} nickname - 账号昵称
 * @returns {Object} 配置对象
 */
function generateConfigTemplate(params, nickname = '账号1') {
    return {
        accounts: [
            {
                nickname: nickname,
                userId: params.userId || '请填入用户ID',
                passToken: params.passToken || '请填入认证令牌',
                playletApiSt: params.playletApiSt || '请填入API状态令牌',
                did: params.did || '请填入设备ID',
                kssig: params.kssig || '请填入签名cookie'
            }
        ],
        settings: {
            userAgent: params.headers['User-Agent'] || 'Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)-ksad-android-********',
            appVersion: params.body.appVersion || '*******',
            appId: params.body.appId || '**********',
            version: params.body.version || '********',
            pkgId: params.headers['Ks-PkgId'] || 'com.kwai.theater1c48a12657a227fa339710301806365b',
            browserUa: params.headers['BrowserUa'] || '',
            systemUa: params.headers['SystemUa'] || '',
            baseUrl: 'https://tube.e.kuaishou.com/rest/e/tube/inspire',
            requestTimeout: 30000,
            retryCount: 3,
            delayBetweenTasks: 30000
        },
        endpoints: {
            ad: '/ad',
            treasureBox: '/treasureBox',
            coin: '/coin',
            boxInfo: '/treasureBox',
            box: '/treasureBox',
            boxAd: '/treasureBox'
        }
    };
}

/**
 * 分析签名规律
 * @param {Array} curlSamples - 多个curl命令样本
 * @returns {Object} 分析结果
 */
function analyzeSignaturePattern(curlSamples) {
    const analysis = {
        signatures: [],
        timestamps: [],
        patterns: [],
        recommendations: []
    };

    curlSamples.forEach((curl, index) => {
        const params = extractParametersFromCurl(curl);
        
        analysis.signatures.push({
            index: index,
            signature: params.signature,
            timestamp: params.body.timestamp || Date.now(),
            url: params.url,
            userId: params.userId
        });
    });

    // 分析签名长度
    const signatureLengths = analysis.signatures.map(s => s.signature.length);
    const uniqueLengths = [...new Set(signatureLengths)];
    
    if (uniqueLengths.length === 1) {
        analysis.patterns.push(`签名长度固定: ${uniqueLengths[0]} 字符`);
    } else {
        analysis.patterns.push(`签名长度变化: ${uniqueLengths.join(', ')} 字符`);
    }

    // 分析签名字符集
    const allChars = analysis.signatures.map(s => s.signature).join('');
    const uniqueChars = [...new Set(allChars)].sort();
    analysis.patterns.push(`使用字符集: ${uniqueChars.join('')}`);

    // 检查是否有重复签名
    const uniqueSignatures = [...new Set(analysis.signatures.map(s => s.signature))];
    if (uniqueSignatures.length < analysis.signatures.length) {
        analysis.patterns.push('存在重复签名，可能与时间戳无关');
    } else {
        analysis.patterns.push('所有签名都不同，可能与时间戳相关');
    }

    // 生成建议
    analysis.recommendations.push('需要更多样本来分析签名算法');
    analysis.recommendations.push('建议提供不同时间、不同任务类型的curl命令');
    analysis.recommendations.push('可以尝试逆向APP获取签名算法');

    return analysis;
}

/**
 * 主函数 - 命令行工具
 */
function main() {
    console.log('=== 喜翻参数提取工具 ===\n');

    // 示例curl命令（用户需要替换为实际的）
    const exampleCurl = `curl -X POST 'https://tube.e.kuaishou.com/rest/e/tube/inspire/treasureBox' -H 'User-Agent: Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)-ksad-android-********' -H 'Connection: Keep-Alive' -H 'Accept-Encoding: gzip' -H 'Content-Type: application/json' -H 'Ks-Sig3: c4d5868667202f9dc78c8f8eb578979115f503fe919d9385' -H 'Ks-Encoding: 2' -H 'Cookie: kssig=MC4wLjAuMA==;playlet.api_st=Cg5wbGF5bGV0LmFwaS5zdBKQAfYW6QE9kq8xqG6oWmbubviqNrMoo1S-vg8MYxq6_wX28BoWbD6s7d5w13DNvJpKSq1H_wSPcS_NEfiyaXaTQQc-Fb58am0sqZwVnV4fS-Oe3udSocTu8A8wIOMsSGbmn58dogzHqB8H3iHdJjmuNSrSezxrovkeEB1btBgObgIU_iSYPiwOSwuiaQK89vaeLxoSST-fVm0IXlPfX7DbP16pxF6AIiClM8SOgS07d2jCCQWFlp1bhEwllnZdgUMGn_hRz3Mw1SgFMAE;userId=142060027607312;did=ANDROID_98820f9934e118f0;passToken=ChNwYXNzcG9ydC5wYXNzLXRva2VuEqAB4tE17ZQTbqSfIiGkrq7uIEHplwlequRb4D088X-DT-kR63NN3S7seDX5o2OVe_lJRT_4_x45OHbwLqmfyHk3JgMGYjUVFgnw45tezfGhADK3by2HOXSsG_Om3ZA3NvWDM_Vg1e4SMAhORFJwVf4JpLWICmti8BdqRp2mZAZMOZr8yaTlcorU3YfVjss0aqO4b729o7lXeoafbeI-H4ZI0hoS5nMSVP4iSf2822lBvlt2jDi3IiCmpdgMyAdhyJBGlqeKF4DE_B6YqshURWCbVaPOVHDBuSgFMAE;' -d '{"version":"********","appVersion":"*******","appId":"**********","message":"加密消息内容"}'`;

    console.log('1. 从示例curl命令提取参数：');
    const params = extractParametersFromCurl(exampleCurl);
    
    console.log('提取的关键参数：');
    console.log(`- userId: ${params.userId}`);
    console.log(`- passToken: ${params.passToken.substring(0, 20)}...`);
    console.log(`- playletApiSt: ${params.playletApiSt.substring(0, 20)}...`);
    console.log(`- did: ${params.did}`);
    console.log(`- kssig: ${params.kssig}`);
    console.log(`- signature: ${params.signature}`);

    console.log('\n2. 生成配置文件模板：');
    const config = generateConfigTemplate(params);
    console.log(JSON.stringify(config, null, 2));

    console.log('\n3. 使用说明：');
    console.log('- 将上述配置保存为 config.json 文件');
    console.log('- 替换示例参数为你的实际参数');
    console.log('- 运行 node 喜翻本地版.js 开始执行任务');
    
    console.log('\n4. 重要提醒：');
    console.log('- 当前签名算法为简化版本，可能需要完善');
    console.log('- 建议提供多个不同时间的curl样本来分析签名规律');
    console.log('- 加密/解密算法需要根据实际情况实现');
}

// 导出函数供其他模块使用
module.exports = {
    extractParametersFromCurl,
    generateConfigTemplate,
    analyzeSignaturePattern
};

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main();
}
