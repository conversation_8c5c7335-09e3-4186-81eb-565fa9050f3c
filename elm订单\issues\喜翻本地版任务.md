# 喜翻本地版自动任务开发记录

## 任务概述

开发一个本地化的喜翻APP自动任务脚本，替代原有的代理服务器方案，实现：
- 广告任务自动执行
- 宝箱任务自动处理
- 多账号并发支持
- 状态管理和黑鸡检测

## 实施计划

### ✅ 已完成的步骤

1. **配置文件设计** (`config.json`)
   - 账号认证参数管理
   - 全局设置配置
   - 接口端点定义

2. **核心请求类** (`XiFanLocal.js`)
   - HTTP请求封装
   - 签名生成框架
   - 加密/解密接口
   - 动态参数生成

3. **任务管理器** (`TaskManager.js`)
   - 广告任务处理
   - 宝箱任务流程
   - 状态持久化
   - 黑鸡检测机制

4. **主程序入口** (`喜翻本地版.js`)
   - 多账号并发执行
   - 配置验证
   - 错误处理
   - 结果统计

5. **辅助工具**
   - 参数提取工具 (`参数提取工具.js`)
   - 使用说明文档 (`README_喜翻本地版.md`)

### 🔄 待完善的部分

1. **加密算法实现**
   - 签名生成算法 (`generateSignature`)
   - 消息加密算法 (`generateMessage`)
   - 响应解密算法 (`decryptResponse`)

2. **接口端点确认**
   - 不同任务类型的具体URL
   - 请求参数格式验证
   - 响应数据结构分析

3. **参数时效性处理**
   - 认证令牌刷新机制
   - 过期检测和重新获取

## 技术要点

### 关键参数说明

从curl命令中需要提取的关键参数：

```javascript
{
  userId: "142060027607312",           // 用户ID
  passToken: "ChNwYXNzcG9ydC5wYXNzLXRva2VuEqAB...", // 认证令牌
  playletApiSt: "Cg5wbGF5bGV0LmFwaS5zdBKQAfYW6QE9...", // API状态令牌
  did: "ANDROID_98820f9934e118f0",     // 设备ID
  kssig: "MC4wLjAuMA==",              // 签名cookie
  signature: "c4d5868667202f9dc78c8f8eb578979115f503fe919d9385" // 请求签名
}
```

### 请求流程

1. **构建请求头**
   - 设置User-Agent和其他固定头
   - 生成动态签名 (Ks-Sig3)
   - 构建Cookie字符串

2. **生成请求体**
   - 加密任务相关数据
   - 包装为message字段
   - 添加版本信息

3. **发送请求**
   - POST到对应接口
   - 处理响应状态
   - 解密响应数据

4. **结果处理**
   - 解析任务结果
   - 检测异常状态
   - 更新本地状态

### 文件结构

```
elm订单/
├── 喜翻本地版.js          # 主程序入口
├── XiFanLocal.js          # 核心请求处理类
├── TaskManager.js         # 任务管理器
├── 参数提取工具.js        # 参数提取辅助工具
├── config.json            # 配置文件
├── README_喜翻本地版.md   # 使用说明
└── issues/
    └── 喜翻本地版任务.md  # 开发记录
```

## 使用指南

### 1. 参数获取

用户需要通过抓包工具获取以下参数：
- 从Cookie中提取认证信息
- 从Headers中提取签名和设备信息
- 从请求体中提取应用版本信息

### 2. 配置设置

编辑 `config.json` 文件，填入获取的参数：

```json
{
  "accounts": [
    {
      "nickname": "账号1",
      "userId": "你的用户ID",
      "passToken": "你的认证令牌",
      "playletApiSt": "你的API状态令牌",
      "did": "你的设备ID",
      "kssig": "你的签名cookie"
    }
  ]
}
```

### 3. 运行脚本

```bash
# 安装依赖
npm install axios

# 运行主程序
node 喜翻本地版.js

# 运行参数提取工具
node 参数提取工具.js
```

## 已知限制

1. **加密算法**：当前使用简化版本，需要逆向实际算法
2. **签名生成**：固定签名值，需要分析生成规律
3. **接口端点**：部分端点URL需要确认
4. **参数时效**：认证令牌可能有时效性

## 后续优化方向

1. **完善加密算法**
   - 分析多个curl样本
   - 逆向APP加密函数
   - 实现真实的签名算法

2. **增强错误处理**
   - 网络异常重试
   - 参数过期自动刷新
   - 更详细的错误分类

3. **功能扩展**
   - 支持更多任务类型
   - 添加任务调度功能
   - 实现Web管理界面

## 测试计划

1. **单账号测试**
   - 验证基础请求功能
   - 测试任务执行流程
   - 检查状态管理

2. **多账号测试**
   - 并发执行稳定性
   - 资源占用情况
   - 错误隔离效果

3. **长期运行测试**
   - 连续多日执行
   - 参数时效性验证
   - 异常恢复能力

## 总结

本地版脚本已完成基础框架开发，核心功能模块齐全。主要待完善的是加密算法部分，需要用户提供更多curl样本或逆向分析来实现真实的签名和加密算法。

脚本设计考虑了扩展性和维护性，支持多账号、错误处理、状态管理等企业级功能，为后续优化奠定了良好基础。
