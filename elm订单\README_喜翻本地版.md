# 喜翻本地版自动任务脚本

## 功能说明

本脚本实现了喜翻APP的本地化自动任务执行，无需依赖代理服务器，支持：

- ✅ 广告任务自动执行
- ✅ 宝箱任务自动开启
- ✅ 宝箱视频广告自动观看
- ✅ 金币余额查询
- ✅ 多账号并发执行
- ✅ 黑鸡状态检测
- ✅ 任务状态持久化

## 文件结构

```
elm订单/
├── 喜翻本地版.js      # 主程序入口
├── XiFanLocal.js      # 核心请求处理类
├── TaskManager.js     # 任务管理器
├── config.json        # 配置文件
└── README_喜翻本地版.md # 使用说明
```

## 安装依赖

```bash
npm install axios
```

## 配置说明

### 1. 获取必需参数

你需要从喜翻APP抓包获取以下参数：

#### 从Cookie中提取：
- `userId` - 用户ID
- `passToken` - 认证令牌
- `playlet.api_st` - API状态令牌  
- `did` - 设备ID
- `kssig` - 签名cookie

#### 从Headers中提取：
- `User-Agent` - 用户代理（已预设）
- `Ks-Sig3` - 请求签名（需要算法）

### 2. 配置文件示例

编辑 `config.json` 文件：

```json
{
  "accounts": [
    {
      "nickname": "账号1",
      "userId": "***************",
      "passToken": "你的认证令牌",
      "playletApiSt": "你的API状态令牌",
      "did": "ANDROID_98820f9934e118f0",
      "kssig": "MC4wLjAuMA=="
    }
  ],
  "settings": {
    "userAgent": "Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)-ksad-android-3.3.55.2",
    "appVersion": "2.7.4.1",
    "appId": "**********",
    "version": "3.3.55.2",
    "baseUrl": "https://tube.e.kuaishou.com/rest/e/tube/inspire"
  }
}
```

## 使用方法

### 1. 运行脚本

```bash
node 喜翻本地版.js
```

### 2. 查看日志

脚本会输出详细的执行日志：

```
=== 喜翻本地版自动任务 ===
启动时间: 2025/1/8 16:19:12

=== 开始执行账号: 账号1 ===
[账号1] ***************: 开始执行完整任务流程
[账号1] ***************: 开始执行广告任务
[账号1] ***************: 发送ad请求...
[账号1] ***************: 广告任务完成 +50金币
```

## 重要说明

### ⚠️ 加密算法限制

当前版本的加密/解密算法是**简化实现**，实际使用需要：

1. **签名算法**：`generateSignature()` 方法需要实现真实的签名算法
2. **消息加密**：`generateMessage()` 方法需要实现真实的加密算法  
3. **响应解密**：`decryptResponse()` 方法需要实现真实的解密算法

### 🔧 完善加密算法的方法

#### 方法1：提供多个curl样本
请提供不同时间、不同任务类型的curl命令，我们可以分析：
- 签名算法的规律
- 加密参数的变化模式
- 时间戳和随机数的影响

#### 方法2：逆向工程
如果你有APP逆向能力，可以：
- 分析APP的加密函数
- 提取签名生成算法
- 实现对应的JavaScript版本

#### 方法3：抓包分析
通过大量抓包数据分析：
- 请求参数的变化规律
- 响应数据的解密方式
- 签名验证的逻辑

## 状态管理

- 每个账号的任务状态保存在 `xf_status_用户ID.json` 文件中
- 防止同一天重复执行任务
- 检测到黑鸡状态时自动停止并标记完成

## 错误处理

脚本包含完善的错误处理机制：
- 网络请求超时重试
- 配置文件验证
- 参数完整性检查
- 异常状态检测

## 多账号支持

在 `config.json` 的 `accounts` 数组中添加多个账号配置即可实现并发执行。

## 注意事项

1. **参数时效性**：认证令牌可能有时效性，需要定期更新
2. **请求频率**：脚本已内置随机延时，避免请求过于频繁
3. **黑鸡检测**：检测到异常奖励时会自动停止执行
4. **日志记录**：建议保存执行日志用于问题排查

## 技术支持

如需完善加密算法或遇到问题，请提供：
1. 完整的curl命令样本（多个不同时间的）
2. 错误日志信息
3. 账号配置信息（脱敏处理）
