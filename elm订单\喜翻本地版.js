const fs = require('fs');
const path = require('path');
const XiFanLocal = require('./XiFanLocal');
const TaskManager = require('./TaskManager');

/**
 * 喜翻本地版主程序
 * 支持多账号并发执行，无需代理服务器
 */

/**
 * 加载配置文件
 * @returns {Object} 配置对象
 */
function loadConfig() {
    const configPath = path.join(__dirname, 'config.json');
    
    if (!fs.existsSync(configPath)) {
        console.error('配置文件不存在，请先创建 config.json 文件');
        console.log('配置文件模板：');
        console.log(JSON.stringify({
            accounts: [{
                nickname: "账号1",
                userId: "你的用户ID",
                passToken: "你的认证令牌",
                playletApiSt: "你的API状态令牌",
                did: "你的设备ID",
                kssig: "你的签名cookie"
            }],
            settings: {
                userAgent: "Dalvik/2.1.0 (Linux; U; Android 15; 24031PN0DC Build/AQ3A.240627.003)-ksad-android-3.3.55.2",
                appVersion: "2.7.4.1",
                appId: "**********",
                version: "3.3.55.2"
            }
        }, null, 2));
        process.exit(1);
    }

    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        return config;
    } catch (error) {
        console.error('配置文件解析失败:', error.message);
        process.exit(1);
    }
}

/**
 * 验证账号配置
 * @param {Object} account - 账号配置
 * @returns {boolean} 是否有效
 */
function validateAccount(account) {
    const requiredFields = ['userId', 'passToken', 'playletApiSt', 'did', 'kssig'];
    
    for (const field of requiredFields) {
        if (!account[field] || account[field].trim() === '') {
            console.error(`账号 ${account.nickname || '未知'} 缺少必需字段: ${field}`);
            return false;
        }
    }
    
    return true;
}

/**
 * 执行单个账号的任务
 * @param {Object} account - 账号配置
 * @param {Object} settings - 全局设置
 * @param {Object} endpoints - 接口端点
 * @returns {boolean} 执行结果
 */
async function executeAccountTasks(account, settings, endpoints) {
    console.log(`\n=== 开始执行账号: ${account.nickname} ===`);
    
    try {
        // 验证账号配置
        if (!validateAccount(account)) {
            return false;
        }

        // 创建客户端实例
        const client = new XiFanLocal(account, settings, endpoints);
        
        // 创建任务管理器
        const taskManager = new TaskManager(client);
        
        // 执行完整任务流程
        const success = await taskManager.executeFullTaskFlow();
        
        if (success) {
            console.log(`账号 ${account.nickname} 任务执行成功`);
        } else {
            console.log(`账号 ${account.nickname} 任务执行失败`);
        }
        
        return success;
        
    } catch (error) {
        console.error(`账号 ${account.nickname} 执行异常:`, error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('=== 喜翻本地版自动任务 ===');
    console.log('启动时间:', new Date().toLocaleString());
    
    try {
        // 加载配置
        const config = loadConfig();
        
        if (!config.accounts || config.accounts.length === 0) {
            console.error('配置文件中没有账号信息');
            return;
        }

        console.log(`加载了 ${config.accounts.length} 个账号配置`);
        
        // 并发执行所有账号的任务
        const results = await Promise.allSettled(
            config.accounts.map(account => 
                executeAccountTasks(account, config.settings, config.endpoints)
            )
        );
        
        // 统计执行结果
        let successCount = 0;
        let failCount = 0;
        
        results.forEach((result, index) => {
            const account = config.accounts[index];
            if (result.status === 'fulfilled' && result.value) {
                successCount++;
            } else {
                failCount++;
                if (result.status === 'rejected') {
                    console.error(`账号 ${account.nickname} 执行被拒绝:`, result.reason);
                }
            }
        });
        
        console.log('\n=== 执行结果统计 ===');
        console.log(`成功: ${successCount} 个账号`);
        console.log(`失败: ${failCount} 个账号`);
        console.log(`总计: ${config.accounts.length} 个账号`);
        console.log('完成时间:', new Date().toLocaleString());
        
    } catch (error) {
        console.error('程序执行异常:', error.message);
        console.error(error.stack);
    }
}

/**
 * 程序入口点
 */
if (require.main === module) {
    main().catch(error => {
        console.error('未捕获的异常:', error);
        process.exit(1);
    });
}

module.exports = {
    main,
    loadConfig,
    executeAccountTasks
};
