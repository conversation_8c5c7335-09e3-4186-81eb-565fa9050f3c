const axios = require("axios");
const fs = require("fs");
const crypto = require("crypto");

/**
 * 喜翻本地化自动任务类
 * 实现本地签名、加密和请求处理，无需代理服务器
 */
class XiFanLocal {
    /**
     * 构造函数
     * @param {Object} account - 账号配置
     * @param {Object} settings - 全局设置
     * @param {Object} endpoints - 接口端点配置
     */
    constructor(account, settings, endpoints) {
        this.account = account;
        this.settings = settings;
        this.endpoints = endpoints;
        this.nickname = account.nickname;
        this.userId = account.userId;
        
        // 请求头模板
        this.defaultHeaders = {
            'User-Agent': settings.userAgent,
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip',
            'Content-Type': 'application/json',
            'Ks-Encoding': '2',
            'BrowserUa': settings.browserUa,
            'SystemUa': settings.systemUa,
            'Ks-PkgId': settings.pkgId,
            'Content-Type': 'application/json; charset=utf-8'
        };
    }

    /**
     * 日志输出
     * @param {string} message - 日志消息
     */
    log(message) {
        console.log(`[${this.nickname}] ${this.userId}: ${message}`);
    }

    /**
     * 生成时间戳（毫秒）
     * @returns {number} 当前时间戳
     */
    generateTimestamp() {
        return Date.now();
    }

    /**
     * 生成随机字符串
     * @param {number} length - 字符串长度
     * @returns {string} 随机字符串
     */
    generateRandomString(length = 32) {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 生成签名 (Ks-Sig3)
     * 注意：这是一个简化版本，实际签名算法可能更复杂
     * @param {Object} params - 签名参数
     * @returns {string} 签名字符串
     */
    generateSignature(params) {
        // 这里需要根据实际的签名算法来实现
        // 目前使用固定值，用户需要提供多个样本来分析规律
        return "c4d5868667202f9dc78c8f8eb578979115f503fe919d9385";
    }

    /**
     * 构建Cookie字符串
     * @returns {string} Cookie字符串
     */
    buildCookieString() {
        return [
            `kssig=${this.account.kssig}`,
            `playlet.api_st=${this.account.playletApiSt}`,
            `userId=${this.account.userId}`,
            `did=${this.account.did}`,
            `passToken=${this.account.passToken}`
        ].join(';');
    }

    /**
     * 生成请求消息体（加密部分）
     * 注意：这是一个占位实现，实际需要逆向APP的加密算法
     * @param {string} taskType - 任务类型
     * @param {Object} extraParams - 额外参数
     * @returns {string} 加密的消息体
     */
    generateMessage(taskType, extraParams = {}) {
        // 这里应该实现实际的加密算法
        // 目前返回示例数据，用户需要提供实际的加密实现
        const baseMessage = {
            version: this.settings.version,
            appVersion: this.settings.appVersion,
            appId: this.settings.appId,
            taskType: taskType,
            timestamp: this.generateTimestamp(),
            userId: this.userId,
            ...extraParams
        };
        
        // 实际应该加密这个对象
        // 这里暂时返回base64编码的JSON（仅用于测试）
        return Buffer.from(JSON.stringify(baseMessage)).toString('base64');
    }

    /**
     * 构建完整的请求配置
     * @param {string} taskType - 任务类型
     * @param {Object} extraParams - 额外参数
     * @returns {Object} axios请求配置
     */
    buildRequestConfig(taskType, extraParams = {}) {
        const timestamp = this.generateTimestamp();
        const signature = this.generateSignature({ taskType, timestamp });
        const message = this.generateMessage(taskType, extraParams);
        
        const endpoint = this.endpoints[taskType] || this.endpoints.treasureBox;
        const url = this.settings.baseUrl + endpoint;
        
        return {
            method: 'POST',
            url: url,
            headers: {
                ...this.defaultHeaders,
                'Ks-Sig3': signature,
                'Cookie': this.buildCookieString()
            },
            data: {
                version: this.settings.version,
                appVersion: this.settings.appVersion,
                appId: this.settings.appId,
                message: message
            },
            timeout: this.settings.requestTimeout
        };
    }

    /**
     * 解密响应数据
     * 注意：这需要实际的解密算法
     * @param {string} encryptedData - 加密的响应数据
     * @returns {Object} 解密后的数据
     */
    decryptResponse(encryptedData) {
        try {
            // 这里应该实现实际的解密算法
            // 目前假设数据是base64编码的JSON（仅用于测试）
            const decrypted = Buffer.from(encryptedData, 'base64').toString('utf8');
            return JSON.parse(decrypted);
        } catch (error) {
            this.log(`解密失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 发送请求
     * @param {string} taskType - 任务类型
     * @param {Object} extraParams - 额外参数
     * @returns {Object} 响应数据
     */
    async sendRequest(taskType, extraParams = {}) {
        const config = this.buildRequestConfig(taskType, extraParams);
        
        try {
            this.log(`发送${taskType}请求...`);
            const response = await axios(config);
            
            if (response.data.result === 1) {
                // 解密响应数据
                if (response.data.data) {
                    const decryptedData = this.decryptResponse(response.data.data);
                    return {
                        success: true,
                        data: decryptedData,
                        raw: response.data
                    };
                }
                return {
                    success: true,
                    data: response.data,
                    raw: response.data
                };
            } else {
                this.log(`请求失败: ${response.data.errorMsg || '未知错误'}`);
                return {
                    success: false,
                    error: response.data.errorMsg || '未知错误',
                    raw: response.data
                };
            }
        } catch (error) {
            this.log(`网络请求失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                raw: null
            };
        }
    }

    /**
     * 延时函数
     * @param {number} ms - 延时毫秒数
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 生成随机延时
     * @param {number} base - 基础延时（秒）
     * @param {number} range - 随机范围（秒）
     * @returns {number} 延时毫秒数
     */
    getRandomDelay(base = 30, range = 10) {
        return (base + Math.floor(Math.random() * range)) * 1000;
    }
}

module.exports = XiFanLocal;
