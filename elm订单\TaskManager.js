const fs = require('fs');

/**
 * 任务管理器 - 处理各种任务类型和状态管理
 */
class TaskManager {
    constructor(xiFanClient) {
        this.client = xiFanClient;
        this.userId = xiFanClient.userId;
        this.nickname = xiFanClient.nickname;
    }

    /**
     * 日志输出
     * @param {string} message - 日志消息
     */
    log(message) {
        this.client.log(message);
    }

    /**
     * 获取状态文件路径
     * @returns {string} 状态文件路径
     */
    getStatusFilePath() {
        return `xf_status_${this.userId}.json`;
    }

    /**
     * 获取今日日期标识
     * @returns {string} 日期标识 (YYYYMMDD格式)
     */
    getTodayDateString() {
        const date = new Date();
        return `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;
    }

    /**
     * 检查今日任务状态
     * @returns {boolean} true=今日已完成，false=未完成
     */
    checkTodayStatus() {
        const statusFile = this.getStatusFilePath();
        const today = this.getTodayDateString();

        try {
            if (fs.existsSync(statusFile)) {
                const data = JSON.parse(fs.readFileSync(statusFile, 'utf8'));
                if (data.date === today && data.completed) {
                    this.log("今日任务已完成，跳过执行");
                    return true;
                }
            }
            return false;
        } catch (error) {
            this.log(`状态检查失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 标记今日任务完成
     * @param {string} reason - 完成原因
     */
    markTodayCompleted(reason = "正常完成") {
        const statusFile = this.getStatusFilePath();
        const today = this.getTodayDateString();
        
        const statusData = {
            date: today,
            completed: true,
            reason: reason,
            timestamp: Date.now()
        };

        try {
            fs.writeFileSync(statusFile, JSON.stringify(statusData, null, 2));
            this.log(`任务状态已保存: ${reason}`);
        } catch (error) {
            this.log(`状态保存失败: ${error.message}`);
        }
    }

    /**
     * 执行广告任务
     * @returns {boolean} 任务是否成功
     */
    async executeAdTask() {
        this.log("开始执行广告任务");
        
        const result = await this.client.sendRequest('ad');
        
        if (!result.success) {
            this.log(`广告任务失败: ${result.error}`);
            return false;
        }

        // 处理广告任务响应
        if (result.data) {
            const amount = result.data.amount || 0;
            const toast = result.data.toast || "广告任务完成";
            
            this.log(`${toast} +${amount}金币`);
            
            // 检测黑鸡状态
            if (amount <= 1 || amount === 50) {
                this.log("检测到异常奖励，可能已被限制");
                this.markTodayCompleted("检测到黑鸡状态");
                return false;
            }
            
            return true;
        }
        
        return false;
    }

    /**
     * 查询宝箱信息
     * @returns {Object|null} 宝箱信息
     */
    async queryTreasureBoxInfo() {
        this.log("查询宝箱信息");
        
        const result = await this.client.sendRequest('boxInfo');
        
        if (!result.success) {
            this.log(`宝箱查询失败: ${result.error}`);
            return null;
        }

        if (result.data) {
            const boxInfo = {
                canOpen: false,
                taskToken: result.data.taskToken || '',
                stageIndex: '',
                buttonText: result.data.popupInfo?.buttonInfo?.buttonText || ''
            };

            // 检查是否可以开宝箱
            boxInfo.canOpen = boxInfo.buttonText === '立即开宝箱';
            
            // 查找当前可领取的阶段
            if (result.data.popupInfo?.stages) {
                for (const stage of result.data.popupInfo.stages) {
                    if (stage.stageIndex === "") break;
                    if (stage.amount > 0) {
                        boxInfo.stageIndex = stage.stageIndex;
                    }
                }
            }

            this.log(`宝箱状态: ${boxInfo.canOpen ? '可开启' : '冷却中'}`);
            return boxInfo;
        }

        return null;
    }

    /**
     * 开启宝箱
     * @param {string} taskToken - 任务令牌
     * @param {string} stageIndex - 阶段索引
     * @returns {Object|null} 开箱结果
     */
    async openTreasureBox(taskToken, stageIndex) {
        this.log("开启宝箱");
        
        const result = await this.client.sendRequest('box', {
            taskToken: taskToken,
            stageIndex: stageIndex
        });
        
        if (!result.success) {
            this.log(`开箱失败: ${result.error}`);
            return null;
        }

        if (result.data) {
            const amount = result.data.amount || 0;
            this.log(`开箱获得: ${amount}金币`);
            
            // 检查是否可以看视频获得额外奖励
            const canWatchVideo = result.data.nextStage?.popupInfo?.buttonInfo?.buttonText?.indexOf('看视频领') > -1;
            
            return {
                amount: amount,
                canWatchVideo: canWatchVideo
            };
        }

        return null;
    }

    /**
     * 观看宝箱视频广告
     * @returns {boolean} 是否成功
     */
    async watchTreasureBoxAd() {
        this.log("观看宝箱视频广告");
        
        const result = await this.client.sendRequest('boxAd');
        
        if (!result.success) {
            this.log(`视频广告失败: ${result.error}`);
            return false;
        }

        if (result.data) {
            const amount = result.data.amount || 0;
            this.log(`视频广告获得: ${amount}金币`);
            return true;
        }

        return false;
    }

    /**
     * 查询金币余额
     * @returns {Object|null} 余额信息
     */
    async queryCoinBalance() {
        this.log("查询金币余额");
        
        const result = await this.client.sendRequest('coin');
        
        if (!result.success) {
            this.log(`余额查询失败: ${result.error}`);
            return null;
        }

        if (result.data && result.data.accountInfoV2) {
            const coinAccount = result.data.accountInfoV2.coinAccount || {};
            const cashAccount = result.data.accountInfoV2.cashAccount || {};
            
            const balanceInfo = {
                coinAmount: coinAccount.amount || 0,
                coinTitle: coinAccount.title || '金币',
                cashAmount: cashAccount.amount || 0,
                cashTitle: cashAccount.title || '现金'
            };

            this.log(`${balanceInfo.coinTitle}: ${balanceInfo.coinAmount} | ${balanceInfo.cashTitle}: ${balanceInfo.cashAmount}`);
            return balanceInfo;
        }

        return null;
    }

    /**
     * 执行完整的任务流程
     * @returns {boolean} 任务是否成功完成
     */
    async executeFullTaskFlow() {
        try {
            // 检查今日状态
            if (this.checkTodayStatus()) {
                return true;
            }

            this.log("开始执行完整任务流程");

            // 1. 执行广告任务
            const adSuccess = await this.executeAdTask();
            if (!adSuccess) {
                return false; // 广告任务失败或检测到黑鸡，停止执行
            }

            // 任务间隔
            const delay = this.client.getRandomDelay(30, 10);
            this.log(`等待 ${Math.floor(delay / 1000)} 秒后执行宝箱任务`);
            await this.client.sleep(delay);

            // 2. 宝箱任务流程
            const boxInfo = await this.queryTreasureBoxInfo();
            if (boxInfo && boxInfo.canOpen) {
                // 开宝箱
                const openResult = await this.openTreasureBox(boxInfo.taskToken, boxInfo.stageIndex);
                if (openResult && openResult.canWatchVideo) {
                    // 观看视频广告
                    await this.watchTreasureBoxAd();
                }
            }

            // 3. 查询最终余额
            await this.queryCoinBalance();

            this.log("任务流程执行完成");
            this.markTodayCompleted("正常完成");
            return true;

        } catch (error) {
            this.log(`任务执行异常: ${error.message}`);
            return false;
        }
    }
}

module.exports = TaskManager;
