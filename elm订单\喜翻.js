// 引入HTTP请求库和文件系统模块
const axios = require("axios")
const fs = require("fs");

// 代理服务器IP地址，用于处理加密请求和解密响应
let ip = "*************"

/**
 * 喜翻APP自动化任务类
 * 主要功能：执行广告任务、开宝箱任务、状态管理
 */
class XF{
    /**
     * 构造函数 - 初始化用户信息
     * @param {Object} item - 用户数据对象
     * @param {string} item.userId - 用户ID（从cookie中获取）
     * @param {string} item.nickname - 用户昵称/备注
     */
    constructor(item){
        this.userId = item.userId
        this.nickname = item.nickname
    }

    /**
     * 日志输出函数 - 带用户标识的日志
     * @param {string} str - 要输出的日志内容
     */
    console_log(str){
      console.log(`账号[${this.nickname}] ${this.userId} :${str}`);
    }
    /**
     * 获取任务状态 - 检查今日是否已完成任务（防止重复执行）
     * @returns {boolean} true=今日已完成，false=未完成
     */
    get_status(){
        // 状态文件名，每个用户独立的状态文件
        const filename = `xf_status_${this.userId}.json`
        // 默认状态数据结构
        const defaultData = {
          num: 0,        // 任务计数
          t: "0"         // 时间标记（格式：年月日）
        };

        try {
            let jsonData;
            try {
                // 尝试读取状态文件
                const data = fs.readFileSync(filename, 'utf8');
                jsonData = JSON.parse(data);

                // 生成今日时间标记（年月日格式）
                let d = new Date()
                let val = d.getFullYear() + "" + d.getMonth() + "" + d.getDate()

                // 如果状态文件中的时间标记等于今日，说明今日已完成
                if(jsonData.t == val){
                  this.console_log("停止运行,全部已完成");
                  return true
                }
            } catch (readErr) {
                // 文件不存在时创建默认状态文件
                if (readErr.code === 'ENOENT') {
                    fs.writeFileSync(filename, JSON.stringify(defaultData, null, 2));
                    jsonData = defaultData;
                } else {
                    throw readErr; // 重新抛出其他错误
                }
            }

            return false; // 今日未完成
        } catch (err) {
            console.error('Error processing status file:', err);
            return false; // 出错时假设未完成
        }
    }
    /**
     * 设置任务完成状态 - 标记今日任务已完成
     * 当检测到账号被黑鸡（奖励异常）时调用
     */
    set_status(){
        // 生成今日时间标记
        let d = new Date()
        let val = d.getFullYear() + "" + d.getMonth() + "" + d.getDate()
        const data = {
            num: 0,    // 任务计数重置
            t: val     // 设置为今日时间标记
        };

        try {
            // 写入状态文件，标记今日已完成
            fs.writeFileSync(`xf_status_${this.userId}.json`, JSON.stringify(data, null, 2), 'utf8');
            this.console_log('状态文件已保存!');
        } catch (err) {
            // 忽略写入错误
        }
    }

    /**
     * 解密响应数据 - 通过代理服务器解密API返回的加密数据
     * @param {string} data - 加密的响应数据
     * @returns {Object} 解密后的数据对象
     */
    async getResponseData(data){
        let config = {
            'method': "post",
            'url': `http://${ip}:8239/so/api/xf/getResponseData`, // 代理服务器解密接口
            'headers': {
                'Content-Type': 'application/json'
            },
            'data': {
                "data": data,           // 待解密的数据
                "userId": this.userId   // 用户ID
            }
        }
        let resp = await axios(config)
        // 确保返回对象格式
        return typeof(resp.data) == 'string' ? JSON.parse(resp.data) : resp.data
    }
    /**
     * 获取请求数据 - 通过代理服务器构造加密的API请求数据
     * 对应curl命令中的完整请求配置（headers、body等）
     * @param {string} task_name - 任务名称（如：ad、boxInfo、box、boxAd、coin）
     * @param {string} taskToken - 任务令牌（宝箱任务需要）
     * @param {string} stageIndex - 阶段索引（宝箱任务需要）
     * @returns {string} JSON格式的axios请求配置
     */
    async getReqDataByUserId(task_name, taskToken, stageIndex){
        let config = {
            'method': 'post',
            'url': `http://${ip}:8239/so/api/getReqDataByUserId`, // 代理服务器请求构造接口
            'headers': {
              'Content-Type': 'application/json'
            },
            data: {
                "userId": this.userId,        // 用户ID（从cookie中提取）
                "script_name": "xf",          // 脚本标识
                "task_name": task_name,       // 任务类型
                "task_token": taskToken,      // 任务令牌
                "stageIndex": stageIndex      // 阶段索引
            }
        }
        let resp = await axios(config)
        return resp.data // 返回构造好的请求配置（JSON字符串）
    }
    /**
     * 执行宝箱相关任务 - 处理宝箱信息查询、开宝箱、看视频等操作
     * 对应curl中的treasureBox接口调用
     * @param {string} data - 请求配置数据（JSON字符串）
     * @param {string} task_name - 任务类型（boxInfo/box/boxAd）
     * @returns {Object} 任务执行结果
     */
    async do_box_task(data, task_name){
        // 解析请求配置并发送请求
        let config = JSON.parse(data)
        let {data: result} = await axios(config)

        // 检查API响应状态（result=1表示成功）
        if (result['result'] == 1) {
           this.console_log("等待解密响应体");

            // 检查是否已停止执行
            if (this['stopped']) {
              this.console_log("已停止，跳过后续执行");
              return;
            }

            // 解密响应数据（data字段包含加密内容）
            let dec_data = await this.getResponseData(result["data"]);

            if (dec_data) {
                // 处理宝箱信息查询
                if(task_name == 'boxInfo'){
                    let stageIndex = ''
                    let taskToken = dec_data.taskToken  // 宝箱任务令牌
                    // 检查是否可以开宝箱（按钮文本判断）
                    let can_open_box = dec_data.popupInfo?.buttonInfo?.buttonText == '立即开宝箱'

                    // 遍历宝箱阶段，找到当前可领取的阶段
                    for(let item of dec_data.popupInfo?.stages){
                        if(item.stageIndex == ""){
                            break
                        }
                        if(item.amount > 0){
                          stageIndex = item.stageIndex  // 记录阶段索引
                        }
                    }
                    return {"can_open_box": can_open_box, "taskToken": taskToken, "stageIndex": stageIndex}
                }

                // 处理开宝箱操作
                if(task_name == "box"){
                    // 检查是否可以看视频获得额外奖励
                    let can_open_box = dec_data.nextStage?.popupInfo?.buttonInfo?.buttonText.indexOf('看视频领') > -1
                    this.console_log(`开宝箱金币：${dec_data.amount}`);
                    return {"can_open_box": can_open_box, "taskToken": 0, "stageIndex": 0}
                }

                // 处理宝箱视频奖励
                if(task_name == "boxAd"){
                    this.console_log(`开宝箱视频金币：${dec_data.amount}`);
                }
            } else {
                this.console_log("解密失败，原因：" + dec_data['msg']);
                console.log(dec_data);
            }
        } else {
          this.console_log("请求失败，原因：" + result['msg']);
          console.log(result);
        }
    }

    /**
     * 执行广告任务 - 观看广告获取金币奖励
     * 对应curl中的广告相关接口调用
     * @param {string} data - 请求配置数据（JSON字符串）
     */
    async do_ad_task(data){
        // 解析请求配置并发送请求
        let config = JSON.parse(data)
        let {data: result} = await axios(config)

        // 检查API响应状态
        if (result['result'] == 1) {
          this.console_log("等待解密响应体");

          // 检查是否已停止执行
          if (this['stopped']) {
            this.console_log("已停止，跳过后续执行");
            return;
          }

          // 解密响应数据
          let dec_data = await this.getResponseData(result["data"]);
          if (dec_data) {
            // 输出任务提示和奖励金额
            this.console_log(dec_data["toast"] + " " + dec_data["amount"]);

            // 检测黑鸡状态（奖励异常低或固定值50）
            // 黑鸡：平台检测到异常行为，限制奖励
            if (dec_data.amount <= 1 || dec_data.amount === 50 || dec_data.amount?.amount === 50) {
              this.console_log("已黑鸡，停止运行")
              this.set_status()  // 标记今日已完成，避免继续执行

              if(dec_data["amount"] == 0){
                this.console_log(JSON.stringify(dec_data))  // 输出详细信息用于调试
              }
              // 可选：发送通知（已注释）
              // await notify.sendNotify("喜翻",`${this.userId}--${this.nickname}-已黑鸡`)
              return;
            }
          } else {
            this.console_log("解密失败，原因：" + dec_data['msg']);
            console.log(dec_data);
          }
        } else {
          this.console_log("请求失败，原因：" + result['msg']);
          console.log(result);
        }
    }
    /**
     * 查询今日金币余额 - 获取账户金币和现金余额信息
     * @returns {void} 直接输出余额信息到控制台
     */
    async get_coin(){
      // 获取金币查询请求配置
      let data = await this.getReqDataByUserId('coin')
      let config = JSON.parse(data)
      let {data: result} = await axios(config)

      // 解密响应数据
      let dec_data = await this.getResponseData(result["data"]);

      if(typeof(dec_data) == 'object'){
          // 提取账户信息
          let coinAccount_amount = dec_data.accountInfoV2?.coinAccount?.amount    // 金币余额
          let coinAccount_title = dec_data.accountInfoV2?.coinAccount?.title      // 金币账户标题
          let cashAccount_amount = dec_data.accountInfoV2?.cashAccount?.amount    // 现金余额
          let cashAccount_title = dec_data.accountInfoV2?.cashAccount?.title      // 现金账户标题

          // 输出账户余额信息
          this.console_log(`${coinAccount_title}:${coinAccount_amount}---${cashAccount_title}:${cashAccount_amount}`)
      } else {
        this.console_log("解密失败？" + dec_data)
      }
    }

    /**
     * 主执行函数 - 按顺序执行所有任务
     * 任务流程：检查状态 -> 广告任务 -> 等待 -> 宝箱任务
     */
    async main(){
        // 可选：查询金币余额（调试用，已注释）
        // await this.get_coin()
        // return

        // 检查今日是否已完成任务（防止重复执行）
        if(this.get_status()){
          this.console_log("已黑鸡，停止运行")
          return
        }

        // 1. 执行广告任务（主要收益来源）
        this.console_log("开始执行广告任务");
        let result_ad = await this.getReqDataByUserId('ad')
        await this.do_ad_task(result_ad)

        // 任务间隔等待（避免请求过于频繁）
        let s = get_random() + 30  // 随机等待36-40秒
        this.console_log(`等待${s}秒`);
        await sleep(s * 1000)

        // 2. 执行宝箱任务流程
        this.console_log("开始宝箱任务");

        // 2.1 查询宝箱状态
        let result = await this.getReqDataByUserId('boxInfo')
        let {"can_open_box": can_open_box, "taskToken": taskToken, "stageIndex": stageIndex} = await this.do_box_task(result, 'boxInfo')

        if(can_open_box){
          this.console_log("去开宝箱");

          // 2.2 开宝箱
          result = await this.getReqDataByUserId('box', taskToken, stageIndex)
          let {"can_open_box": can_watch_video} = await this.do_box_task(result, 'box')

          // 2.3 如果可以看视频获得额外奖励
          if(can_watch_video){
            this.console_log("看宝箱视频");
            result = await this.getReqDataByUserId('boxAd')
            await this.do_box_task(result, 'boxAd')
          }
        } else {
          this.console_log("宝箱冷却中");
        }
    }

}

/**
 * 异步延时函数 - 暂停执行指定毫秒数
 * @param {number} ms - 延时毫秒数
 */
const sleep = async (ms) => {
    await new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
};

/**
 * 生成随机数 - 用于任务间隔的随机等待时间
 * @returns {number} 返回6-10之间的随机整数
 */
function get_random() {
    const a = 6,      // 最小值
          b = 5,      // 范围大小
          c = Math.floor(Math.random() * b) + a;  // 生成6-10的随机数
    return c;
}

/**
 * 主程序入口 - 立即执行的异步函数
 * 支持多账号并发执行任务
 */
!(async()=>{
    // 用户账号配置数组
    // userId从喜翻APP的cookie中获取，对应curl命令中Cookie头的userId值
    let userDataArr = [
        {
            "userId": "142060026986591",  // 用户ID（必须从实际cookie中获取）
            "nickname": "账号1"           // 账号备注名称
        },
        {
            "userId": "142060026988911",  // 第二个账号的用户ID
            "nickname": "账号2"           // 第二个账号备注
        }
    ]

    // 并发执行所有账号的任务（同时运行，提高效率）
    await Promise.all(userDataArr.map(async (item) => {
        await new XF(item).main();  // 为每个账号创建XF实例并执行主任务
    }));
})();
