const axios = require("axios")
const fs = require("fs");
let ip = "*************"

class XF{
    constructor(item){
        this.userId = item.userId
        this.nickname = item.nickname
    }
    console_log(str){
      console.log(`账号[${this.nickname}] ${this,this.userId} :${str}`);
    }
 get_status(){
    const filename = `xf_status_${this.userId}.json`
    const defaultData = { 
      num: 0,
      t: "0"
    };

    try {
        // Try to read the file
        let jsonData;
        try {
            const data = fs.readFileSync(filename, 'utf8');
            jsonData = JSON.parse(data);
            let d =  new Date()
            let val = d.getFullYear() +""+ d.getMonth() + ""+d.getDate()
            if(jsonData.t == val){
              // console.log(jsonData)
              this.console_log("停止运行,全部已完成");
              return true
            }
        } catch (readErr) {
            if (readErr.code === 'ENOENT') {
                // File doesn't exist, create it with default data
                fs.writeFileSync(filename, JSON.stringify(defaultData, null, 2));
                jsonData = defaultData;
            } else {
                throw readErr; // Re-throw other errors
            }
        }

        return false;
    } catch (err) {
        console.error('Error processing status file:', err);
        return false; // Assume not completed if there's an error
    }
  }
   set_status(){
        let d =  new Date()
        let val = d.getFullYear() +""+ d.getMonth() + ""+d.getDate()
        const data = {
            num: 0,
            t: val
        };
        
        try {
        fs.writeFileSync(`xf_status_${this.userId}.json`, JSON.stringify(data, null, 2), 'utf8');
        this.console_log('File has been saved!');
        } catch (err) {
        // console.error('Error writing file:');
    }
    }


    async getResponseData(data){
        let config = {
            'method': "post",
            'url': `http://${ip}:8239/so/api/xf/getResponseData`,
            'headers': {
            'Content-Type': 'application/json'
            },
            'data': {
                "data": data,
                "userId":this.userId
            }
        }
        let resp = await axios(config)
        return typeof(resp.data)=='string'?JSON.parse(resp.data):resp.data
    }
    async getReqDataByUserId(task_name,taskToken,stageIndex){
        let config ={
            'method': 'post',
            'url': `http://${ip}:8239/so/api/getReqDataByUserId`,
            'headers': {
              'Content-Type': 'application/json'
            },
            data:{
                "userId":this.userId,
                "script_name":"xf",
                "task_name":task_name,
                "task_token":taskToken,
                "stageIndex":stageIndex
            }
          }
          let resp = await axios(config)
          return resp.data
        
    }
    async do_box_task(data,task_name){
        let config =  JSON.parse(data)
        let {data:result} = await axios(config)
        //解密
        if (result['result'] == 1) {
           this.console_log("等待解密响应体");
      
            if (this['stopped']) {
              this.console_log("已停止，跳过后续执行");
              return;
            }
            // console.log(result["data"],'响应data');

            let dec_data = await this.getResponseData(result["data"]);
            
            if (dec_data) {
              // console.log(dec_data,'解密数据');
                if(task_name=='boxInfo'){
                    // console.log(dec_data);
                    let stageIndex = ''
                    let taskToken = dec_data.taskToken
                    let can_open_box = dec_data.popupInfo?.buttonInfo?.buttonText == '立即开宝箱'
                    // console.log(can_open_box);
                    for(let item of dec_data.popupInfo?.stages){
                        if(item.stageIndex==""){
                        break
                        }
                        if(item.amount>0){
                          stageIndex = item.stageIndex
                        }
                    }
                    return {"can_open_box":can_open_box,"taskToken":taskToken,"stageIndex":stageIndex}
                }
                if(task_name=="box"){
                    let can_open_box = dec_data.nextStage?.popupInfo?.buttonInfo?.buttonText.indexOf('看视频领')>-1
                        this.console_log(`开宝箱金币：${dec_data.amount}`);
                        return {"can_open_box":can_open_box,"taskToken":0,"stageIndex":0}
                    }
                if(task_name=="boxAd"){
                       this.console_log(` 开宝箱视频金币：${dec_data.amount}`);
                }
            }else {
                this.console_log(" 解密失败 ,原因" + dec_data['msg']), console["log"](dec_data);
            } 
        }else {
          this.console_log("请求失败,原因" + result['msg']), console["log"](result);
        }
    }

   
    async do_ad_task(data){
        let config =  JSON.parse(data)
        let {data:result} = await axios(config)
        //解密
        if (result['result'] == 1) {
          this.console_log("等待解密响应体");
    
          if (this['stopped']) {
            this.console_log(" 已停止，跳过后续执行");
            return;
          }
          let dec_data = await this.getResponseData(result["data"]);
          if (dec_data) {
            this.console_log(dec_data["toast"] + " " + dec_data["amount"]);
    
            if (dec_data.amount <=1 || dec_data.amount === 50 || dec_data.amount?.amount === 50) {
              this.console_log("已黑鸡，停止运行")
              this.set_status()
              if(dec_data["amount"]==0){
                this.console_log(JSON.stringify(dec_data))
              }
              // await notify.sendNotify("喜翻",`${this.userId}--${this.nick_name}-已黑鸡`)
              return;
            }
          } else {
            this.console_log( "解密失败 ,原因" + dec_data['msg']), console["log"](dec_data);
          }
        } else {
          this.console_log("请求失败,原因" + result['msg']), console["log"](result);
        }
    
    }
    //查询今日金币
    async get_coin(){
      let data = await this.getReqDataByUserId('coin')
      let config =  JSON.parse(data)
      let {data:result} = await axios(config)
      let dec_data = await this.getResponseData(result["data"]);
      if(typeof(dec_data)=='object'){
      //   console.log(JSON.stringify(dec_data),'object_tostring_data');
          let coinAccount_amount = dec_data.accountInfoV2?.coinAccount?.amount
        let coinAccount_title = dec_data.accountInfoV2?.coinAccount?.title
        let cashAccount_amount = dec_data.accountInfoV2?.cashAccount?.amount
        let cashAccount_title = dec_data.accountInfoV2?.cashAccount?.title
        this.console_log(`${coinAccount_title}:${coinAccount_amount}---${cashAccount_title}:${cashAccount_amount}`)
      }else{
        this.console_log("解密失败？"+dec_data)
      }
    }

    async main(){

        //查询金币
        // await this.get_coin()
        // return

        if(this.get_status()){
          this.console_log("已黑鸡，停止运行")
          return
        }
        //广告任务
        let result_ad = await this.getReqDataByUserId('ad')
        // console.log(result);

        await this.do_ad_task(result_ad)


        //宝箱广告如果报错就先不跑

        let s = get_random() + 30
        this.console_log(`等待${s}秒`);
        await sleep(s * 1000)
        //查询宝箱
        let result = await this.getReqDataByUserId('boxInfo')
        let {"can_open_box":can_open_box,"taskToken":taskToken,"stageIndex":stageIndex} = await this.do_box_task(result,'boxInfo')
        if(can_open_box){
          this.console_log("去开宝箱");
            result = await this.getReqDataByUserId('box',taskToken,stageIndex)
            let {"can_open_box":can_watch_video} = await this.do_box_task(result,'box')
            if(can_watch_video){
              this.console_log("看宝箱视频");
                result = await this.getReqDataByUserId('boxAd')
                await this.do_box_task(result,'boxAd')
            }
            
        }else{
          this.console_log(`宝箱冷却中`);
        }


    }

}
const sleep = async (ms) => {
    await new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
  };
function get_random() {
    const a = 6,
          b = 5,
          c = Math["floor"](Math["random"]() * b) + a;
  
    return c;
  }
!(async()=>{
    // 填入你自己喜翻 ck里面的userId
  let userDataArr = [
   {
      "userId":"142060026986591",
      "nickname":"备注"
    },
    {
      "userId":"142060026988911",
      "nickname":"备注"
    }
  ]
    // 并发执行所有 userId 的任务
    await Promise.all(userDataArr.map(async (item) => {
        await new XF(item).main();
    }));
  })();
